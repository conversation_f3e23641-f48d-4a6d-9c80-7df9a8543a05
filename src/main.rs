use clap::{Arg, Command};
use std::path::PathBuf;
use tracing::{info, error};
use tracing_subscriber;

mod downloader;
mod parser;
mod staticizer;
mod error;

use crate::downloader::WebDownloader;
use crate::error::Result;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::init();

    let matches = Command::new("web-to-static")
        .version("0.1.0")
        .about("将网站下载并转换为静态网站")
        .arg(
            Arg::new("url")
                .help("要下载的网站URL")
                .required(true)
                .index(1),
        )
        .arg(
            Arg::new("output")
                .short('o')
                .long("output")
                .value_name("DIR")
                .help("输出目录")
                .default_value("./output"),
        )
        .arg(
            Arg::new("depth")
                .short('d')
                .long("depth")
                .value_name("NUM")
                .help("下载深度")
                .default_value("2"),
        )
        .arg(
            Arg::new("concurrent")
                .short('c')
                .long("concurrent")
                .value_name("NUM")
                .help("并发下载数")
                .default_value("10"),
        )
        .get_matches();

    let url = matches.get_one::<String>("url").unwrap();
    let output_dir = PathBuf::from(matches.get_one::<String>("output").unwrap());
    let depth: usize = matches.get_one::<String>("depth").unwrap().parse()?;
    let concurrent: usize = matches.get_one::<String>("concurrent").unwrap().parse()?;

    info!("开始下载网站: {}", url);
    info!("输出目录: {:?}", output_dir);
    info!("下载深度: {}", depth);
    info!("并发数: {}", concurrent);

    let downloader = WebDownloader::new(concurrent);
    
    match downloader.download_website(url, &output_dir, depth).await {
        Ok(_) => {
            info!("网站下载完成！");
            println!("✅ 网站已成功转换为静态网站，保存在: {:?}", output_dir);
        }
        Err(e) => {
            error!("下载失败: {}", e);
            eprintln!("❌ 下载失败: {}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}
