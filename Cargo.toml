[package]
name = "web-to-static"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
scraper = "0.18"
url = "2.4"
clap = { version = "4.0", features = ["derive"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "1.0"
regex = "1.10"
mime_guess = "2.0"
sha2 = "0.10"
base64 = "0.21"
walkdir = "2.4"
indicatif = "0.17"
tracing = "0.1"
tracing-subscriber = "0.3"

[dev-dependencies]
tempfile = "3.8"
